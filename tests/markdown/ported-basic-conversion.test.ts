/**
 * Ported basic conversion tests from the original lexical-parser
 * These tests ensure the new Markdown class maintains compatibility with the original parser
 */

import { describe, it, expect, beforeEach } from 'vitest';
import { Markdown } from '../../src/markdown';

describe('Ported Basic Conversion Tests', () => {
  let parser: Markdown;

  beforeEach(() => {
    parser = new Markdown();
  });

  afterEach(() => {
    parser.destroy();
  });

  describe('Basic Conversion', () => {
    it('should convert simple text to lexical', async () => {
      const markdown = 'Hello world';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data).toBeDefined();
      expect(result.data?.root.children).toHaveLength(1);
      expect(result.data?.root.children[0].type).toBe('paragraph');
    });

    it('should convert simple lexical to markdown', async () => {
      const lexicalDoc = {
        root: {
          type: 'root' as const,
          children: [{
            type: 'paragraph',
            children: [{
              type: 'text',
              text: 'Hello world',
              detail: 0,
              format: 0,
              mode: 'normal' as const,
              style: '',
              version: 1
            }],
            direction: 'ltr' as const,
            format: '',
            indent: 0,
            version: 1
          }],
          direction: 'ltr' as const,
          format: '',
          indent: 0,
          version: 1
        }
      };

      const result = await parser.lexicalToMarkdown(lexicalDoc);

      expect(result.success).toBe(true);
      expect(result.data).toBe('Hello world');
    });

    it('should handle empty content', async () => {
      const result = await parser.markdownToLexical('');

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(0);
    });
  });

  describe('Headings', () => {
    it('should convert headings correctly', async () => {
      const markdown = '# Heading 1\n## Heading 2\n### Heading 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(3);
      expect(result.data?.root.children[0].type).toBe('extended-heading');
      expect(result.data?.root.children[0].tag).toBe('h1');
      expect(result.data?.root.children[1].tag).toBe('h2');
      expect(result.data?.root.children[2].tag).toBe('h3');
    });

    it('should convert all heading levels', async () => {
      const markdown = '# H1\n## H2\n### H3\n#### H4\n##### H5\n###### H6';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(6);

      const headings = result.data?.root.children;
      expect(headings?.[0].tag).toBe('h1');
      expect(headings?.[1].tag).toBe('h2');
      expect(headings?.[2].tag).toBe('h3');
      expect(headings?.[3].tag).toBe('h4');
      expect(headings?.[4].tag).toBe('h5');
      expect(headings?.[5].tag).toBe('h6');
    });
  });

  describe('Text Formatting', () => {
    it('should convert bold text', async () => {
      const markdown = '**bold text**';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('bold text');
      expect(textNode.format & 1).toBe(1); // Bold format bit
    });

    it('should convert italic text with asterisks', async () => {
      const markdown = '*italic text*';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic format bit
    });

    it('should convert italic text with underscores (round-trip)', async () => {
      const originalMarkdown = '_italic text_';

      // Convert to Lexical
      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const paragraph = lexicalResult.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('italic text');
      expect(textNode.format & 2).toBe(2); // Italic format bit

      // Convert back to Markdown
      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      console.log('Underscore italic round-trip result:', markdownResult.data);

      expect(markdownResult.success).toBe(true);
      // Should convert to Ghost-compatible italic syntax (*text*)
      expect(markdownResult.data).toContain('*italic text*');
      expect(markdownResult.data).not.toContain('_italic text_');
    });

    it('should convert strikethrough text (round-trip)', async () => {
      const originalMarkdown = '~~strikethrough text~~';

      // Convert to Lexical
      const lexicalResult = await parser.markdownToLexical(originalMarkdown);
      expect(lexicalResult.success).toBe(true);

      const paragraph = lexicalResult.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('strikethrough text');
      expect(textNode.format & 4).toBe(4); // Strikethrough format bit

      // Convert back to Markdown
      const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data!);
      console.log('Strikethrough round-trip result:', markdownResult.data);

      expect(markdownResult.success).toBe(true);
      // Should preserve strikethrough syntax
      expect(markdownResult.data).toContain('~~strikethrough text~~');
    });

    it('should convert inline code', async () => {
      const markdown = '`inline code`';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const textNode = paragraph.children[0];
      expect(textNode.text).toBe('inline code');
      expect(textNode.format & 16).toBe(16); // Code format bit
    });

    it('should convert Obsidian tables to Lexical', async () => {
      const markdown = `| Header 1 | Header 2 |
|----------|----------|
| Cell 1   | Cell 2   |
| Cell 3   | Cell 4   |`;

      const result = await parser.markdownToLexical(markdown);

      console.log('Table conversion result:', JSON.stringify(result, null, 2));

      expect(result.success).toBe(true);
      const tableNode = result.data?.root.children[0];
      expect(tableNode.type).toBe('table');
    });

    it('should handle mixed formatting', async () => {
      const markdown = 'This is **bold** and *italic* and `code` text.';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      expect(paragraph.children.length).toBeGreaterThan(1);

      // Should have multiple text nodes with different formatting
      const hasPlainText = paragraph.children.some((node: any) => node.format === 0);
      const hasBoldText = paragraph.children.some((node: any) => (node.format & 1) === 1);
      const hasItalicText = paragraph.children.some((node: any) => (node.format & 2) === 2);
      const hasCodeText = paragraph.children.some((node: any) => (node.format & 16) === 16);

      expect(hasPlainText).toBe(true);
      expect(hasBoldText).toBe(true);
      expect(hasItalicText).toBe(true);
      expect(hasCodeText).toBe(true);
    });
  });

  describe('Lists', () => {
    it('should convert unordered lists', async () => {
      const markdown = '- Item 1\n- Item 2\n- Item 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      expect(result.data?.root.children).toHaveLength(1);

      const list = result.data?.root.children[0];
      expect(list.type).toBe('list');
      expect(list.listType).toBe('bullet');
      expect(list.children).toHaveLength(3);
    });

    it('should convert ordered lists', async () => {
      const markdown = '1. Item 1\n2. Item 2\n3. Item 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const list = result.data?.root.children[0];
      expect(list.type).toBe('list');
      expect(list.listType).toBe('number');
      expect(list.children).toHaveLength(3);
    });

    it('should handle nested lists', async () => {
      const markdown = '- Item 1\n  - Nested item 1\n  - Nested item 2\n- Item 2';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);

      // The official Lexical markdown parser may create separate lists for nested items
      // Let's just verify we have list structures
      const hasListNodes = result.data?.root.children.some((child: any) => child.type === 'list');
      expect(hasListNodes).toBe(true);

      // Verify we have list items
      const hasListItems = result.data?.root.children.some((child: any) =>
        child.type === 'list' && child.children.some((item: any) => item.type === 'listitem')
      );
      expect(hasListItems).toBe(true);
    });
  });

  describe('Links', () => {
    it('should convert simple links', async () => {
      const markdown = '[Link text](https://example.com)';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const linkNode = paragraph.children[0];
      expect(linkNode.type).toBe('link');
      expect(linkNode.url).toBe('https://example.com');
    });

    it('should convert links with titles', async () => {
      const markdown = '[Link text](https://example.com "Link title")';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const paragraph = result.data?.root.children[0];
      const linkNode = paragraph.children[0];
      expect(linkNode.type).toBe('link');
      expect(linkNode.url).toBe('https://example.com');
      expect(linkNode.title).toBe('Link title');
    });
  });

  describe('Code Blocks', () => {
    it('should convert code blocks', async () => {
      const markdown = '```javascript\nconsole.log("Hello, world!");\n```';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const codeBlock = result.data?.root.children[0];
      expect(codeBlock.type).toBe('code');
      expect(codeBlock.language).toBe('javascript');
    });

    it('should convert code blocks without language', async () => {
      const markdown = '```\nsome code\n```';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const codeBlock = result.data?.root.children[0];
      expect(codeBlock.type).toBe('code');
    });
  });

  describe('Blockquotes', () => {
    it('should convert blockquotes', async () => {
      const markdown = '> This is a blockquote';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0];
      expect(quote.type).toBe('quote');
    });

    it('should convert multi-line blockquotes', async () => {
      const markdown = '> Line 1\n> Line 2\n> Line 3';
      const result = await parser.markdownToLexical(markdown);

      expect(result.success).toBe(true);
      const quote = result.data?.root.children[0];
      expect(quote.type).toBe('quote');
      expect(quote.children.length).toBeGreaterThan(0);
    });
  });
});
