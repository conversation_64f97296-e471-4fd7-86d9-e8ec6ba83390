/**
 * Obsidian-Ghost Markdown Parser
 *
 * A clean, simple parser that converts between Markdown and Lexical
 * with support for Obsidian-specific formatting and Ghost-specific nodes.
 */

import { createHeadlessEditor } from '@lexical/headless';
import type { LexicalEditor } from 'lexical';
import { $createTextNode, $createParagraphNode } from 'lexical';
import { $convertFromMarkdownString, $convertToMarkdownString } from '@lexical/markdown';
import { TRANSFORMERS, type TextFormatTransformer, type ElementTransformer, type TextMatchTransformer, type MultilineElementTransformer } from '@lexical/markdown';

import { HeadingNode, QuoteNode, $createQuoteNode, $isQuoteNode } from '@lexical/rich-text';
import { ListItemNode, ListNode } from '@lexical/list';
import { CodeNode } from '@lexical/code';
import { LinkNode, $createLinkNode, $isLinkNode } from '@lexical/link';
import { TableNode, TableRowNode, TableCellNode, $createTableNode, $createTableRowNode, $createTableCellNode, $isTableNode } from '@lexical/table';

/**
 * Obsidian-specific italic transformer for asterisk syntax
 * Enhanced version that handles Obsidian's specific italic behavior
 */
export const OBSIDIAN_ITALIC_STAR: TextFormatTransformer = {
  format: ['italic'],
  tag: '*',
  type: 'text-format',
};

/**
 * Obsidian-specific italic transformer for underscore syntax
 * Enhanced version that handles Obsidian's specific italic behavior
 */
export const OBSIDIAN_ITALIC_UNDERSCORE: TextFormatTransformer = {
  format: ['italic'],
  intraword: false, // Obsidian doesn't allow underscores within words for italic
  tag: '_',
  type: 'text-format',
};

/**
 * Ghost callout transformer for Obsidian-style callouts
 * Converts > [!type] syntax to Ghost callout cards
 */
export const GHOST_CALLOUT: ElementTransformer = {
  dependencies: [QuoteNode],
  export: (node, exportChildren) => {
    if (!$isQuoteNode(node)) {
      return null;
    }

    // Check if this is a callout by looking at the first child
    const firstChild = node.getFirstChild();
    if (firstChild && firstChild.getType() === 'paragraph') {
      const textContent = firstChild.getTextContent();
      const calloutMatch = textContent.match(/^\[!([^\]]+)\]/);
      if (calloutMatch) {
        const calloutType = calloutMatch[1].toLowerCase();
        const content = exportChildren(node);
        // Remove the callout marker from the content
        const cleanContent = content.replace(/^\[![^\]]+\]\s*/, '');
        return `> [!${calloutType}]\n> ${cleanContent.replace(/\n/g, '\n> ')}`;
      }
    }

    return null; // Let the default quote transformer handle it
  },
  regExp: /^>\s*\[!([^\]]+)\]/,
  replace: (parentNode, children, match, isImport) => {
    const calloutType = match[1];
    const node = $createQuoteNode();

    // Create a paragraph with the callout marker
    const markerParagraph = $createParagraphNode();
    const markerText = $createTextNode(`[!${calloutType}]`);
    markerParagraph.append(markerText);

    // Add the marker paragraph and then the children
    node.append(markerParagraph, ...children);
    parentNode.replace(node);

    if (!isImport) {
      node.select(0, 0);
    }
  },
  type: 'element',
};

/**
 * Obsidian wikilink transformer
 * Converts [[target|alias]] syntax to regular links
 */
export const OBSIDIAN_WIKILINK: TextMatchTransformer = {
  dependencies: [LinkNode],
  export: (node, exportChildren, exportFormat) => {
    if (!$isLinkNode(node)) {
      return null;
    }

    // Check if this is a wikilink (has special data attribute or URL pattern)
    const url = node.getURL();
    if (url.startsWith('obsidian://') || node.getTitle()?.includes('wikilink')) {
      const textContent = exportChildren(node);
      const target = url.replace('obsidian://', '');
      return textContent !== target ? `[[${target}|${textContent}]]` : `[[${target}]]`;
    }

    return null; // Let the default link transformer handle it
  },
  importRegExp: /\[\[([^\]|]+)(\|([^\]]+))?\]\]/,
  regExp: /\[\[([^\]|]+)(\|([^\]]+))?\]\]/,
  replace: (textNode, match) => {
    const target = match[1];
    const alias = match[3];
    const linkNode = $createLinkNode(`obsidian://${target}`, {
      title: 'wikilink'
    });
    const textNode2 = $createTextNode(alias || target);
    linkNode.append(textNode2);
    textNode.replace(linkNode);
  },
  trigger: ']]',
  type: 'text-match',
};

/**
 * Obsidian inline math transformer
 * Converts $equation$ syntax to math nodes (represented as code for now)
 */
export const OBSIDIAN_MATH_INLINE: TextMatchTransformer = {
  dependencies: [CodeNode],
  export: (node, exportChildren, exportFormat) => {
    // For now, we'll represent math as inline code with a special marker
    if (node.getType() === 'code' && (node as any).__mathInline) {
      return `$${(node as any).getTextContent()}$`;
    }
    return null;
  },
  importRegExp: /\$([^$\n]+)\$/,
  regExp: /\$([^$\n]+)\$$/,
  replace: (textNode, match) => {
    const equation = match[1];
    // Create a code node to represent the math (since we don't have a dedicated math node)
    const codeNode = $createTextNode(equation);
    // Mark it as math for export
    (codeNode as any).__mathInline = true;
    textNode.replace(codeNode);
  },
  trigger: '$',
  type: 'text-match',
};

/**
 * Obsidian tags transformer
 * Converts #tag syntax to special link nodes
 */
export const OBSIDIAN_TAGS: TextMatchTransformer = {
  dependencies: [LinkNode],
  export: (node, exportChildren, exportFormat) => {
    if (!$isLinkNode(node)) {
      return null;
    }

    // Check if this is a tag link
    const url = node.getURL();
    if (url.startsWith('obsidian://tag/')) {
      const tag = url.replace('obsidian://tag/', '');
      return `#${tag}`;
    }

    return null;
  },
  importRegExp: /#([a-zA-Z0-9_-]+)/,
  regExp: /#([a-zA-Z0-9_-]+)$/,
  replace: (textNode, match) => {
    const tag = match[1];
    const linkNode = $createLinkNode(`obsidian://tag/${tag}`, {
      title: 'tag'
    });
    const textNode2 = $createTextNode(`#${tag}`);
    linkNode.append(textNode2);
    textNode.replace(linkNode);
  },
  trigger: '#',
  type: 'text-match',
};

/**
 * Obsidian table transformer
 * Converts markdown table syntax to Lexical table nodes
 */
export const OBSIDIAN_TABLE: MultilineElementTransformer = {
  dependencies: [TableNode, TableRowNode, TableCellNode],
  export: (node, exportChildren) => {
    if (!$isTableNode(node)) {
      return null;
    }

    const rows = node.getChildren();
    if (rows.length === 0) {
      return null;
    }

    const tableLines: string[] = [];

    rows.forEach((row, rowIndex) => {
      if (row.getType() === 'tablerow') {
        const cells = (row as any).getChildren();
        const cellTexts = cells.map((cell: any) => {
          if (cell.getType() === 'tablecell') {
            return exportChildren(cell).trim();
          }
          return '';
        });

        const rowText = `| ${cellTexts.join(' | ')} |`;
        tableLines.push(rowText);

        // Add separator row after header (first row)
        if (rowIndex === 0) {
          const separator = `|${cellTexts.map(() => '----------|').join('')}`;
          tableLines.push(separator);
        }
      }
    });

    return tableLines.join('\n');
  },
  regExpStart: /^\|(.+)\|$/,
  regExpEnd: { optional: true, regExp: /^(?!\|)/ },
  replace: (rootNode, _children, startMatch, _endMatch, linesInBetween, isImport) => {
    if (!isImport) {
      return;
    }

    // Parse table lines
    const lines = [startMatch[0], ...linesInBetween];
    const tableLines = lines.filter(line => line.trim().startsWith('|') && line.trim().endsWith('|'));

    if (tableLines.length < 2) {
      return; // Need at least header and separator
    }

    // Skip separator line (usually the second line with dashes)
    const dataLines = tableLines.filter((line, index) => {
      if (index === 1) {
        // Check if this is a separator line
        return !line.match(/^\|[\s\-\|:]+\|$/);
      }
      return true;
    });

    if (dataLines.length === 0) {
      return;
    }

    const tableNode = $createTableNode();

    dataLines.forEach((line, rowIndex) => {
      const cells = line.split('|').slice(1, -1); // Remove empty first and last elements
      const rowNode = $createTableRowNode();

      cells.forEach(cellText => {
        const cellNode = $createTableCellNode(rowIndex === 0 ? 1 : 0); // 1 for header, 0 for data
        const paragraphNode = $createParagraphNode();
        const textNode = $createTextNode(cellText.trim());
        paragraphNode.append(textNode);
        cellNode.append(paragraphNode);
        rowNode.append(cellNode);
      });

      tableNode.append(rowNode);
    });

    rootNode.append(tableNode);
  },
  type: 'multiline-element',
};

/**
 * Combined transformers including Obsidian-specific ones
 */
const OBSIDIAN_TRANSFORMERS = [
  // Start with default transformers, but exclude the default italic transformers
  // We keep STRIKETHROUGH, BOLD, INLINE_CODE, etc. but replace italic transformers
  ...TRANSFORMERS.filter(t => {
    if (t.type !== 'text-format') return true;
    const tag = (t as any).tag;
    // Exclude default italic transformers (ITALIC_STAR and ITALIC_UNDERSCORE)
    return tag !== '*' && tag !== '_';
  }),
  // Add our custom text format transformers
  OBSIDIAN_ITALIC_STAR,
  OBSIDIAN_ITALIC_UNDERSCORE,
  // Add our custom element and text-match transformers
  GHOST_CALLOUT,
  OBSIDIAN_TABLE,
  OBSIDIAN_WIKILINK,
  OBSIDIAN_MATH_INLINE,
  OBSIDIAN_TAGS,
];

/**
 * Result of a conversion operation
 */
export interface ConversionResult<T> {
  success: boolean;
  data?: T;
  error?: string;
  warnings?: string[];
}

/**
 * Lexical document representation
 */
export interface LexicalDocument {
  root: any; // Use any for compatibility with Lexical's serialized format
  nodes?: any[]; // For compatibility with original parser
}

/**
 * Base Lexical node interface
 */
export interface LexicalNode {
  type: string;
  version?: number;
  [key: string]: any;
}

/**
 * Lexical root node
 */
export interface LexicalRootNode extends LexicalNode {
  type: 'root';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
  version?: number;
}

/**
 * Lexical text node
 */
export interface LexicalTextNode extends LexicalNode {
  type: 'text';
  text: string;
  detail: number;
  format: number;
  mode: 'normal' | 'token' | 'segmented';
  style: string;
}

/**
 * Lexical paragraph node
 */
export interface LexicalParagraphNode extends LexicalNode {
  type: 'paragraph';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

/**
 * Lexical heading node
 */
export interface LexicalHeadingNode extends LexicalNode {
  type: 'heading';
  tag: 'h1' | 'h2' | 'h3' | 'h4' | 'h5' | 'h6';
  children: LexicalNode[];
  direction?: 'ltr' | 'rtl';
  format?: string;
  indent?: number;
}

/**
 * Lexical list node
 */
export interface LexicalListNode extends LexicalNode {
  type: 'list';
  listType: 'bullet' | 'number' | 'check';
  children: LexicalListItemNode[];
  start?: number;
  tag: 'ul' | 'ol';
}

/**
 * Lexical list item node
 */
export interface LexicalListItemNode extends LexicalNode {
  type: 'listitem';
  children: LexicalNode[];
  checked?: boolean;
  value?: number;
}

/**
 * Text formatting constants - aligned with official Lexical format
 */
export const TEXT_FORMAT = {
  BOLD: 1,           // IS_BOLD = 1
  ITALIC: 2,         // IS_ITALIC = 1 << 1
  STRIKETHROUGH: 4,  // IS_STRIKETHROUGH = 1 << 2
  UNDERLINE: 8,      // IS_UNDERLINE = 1 << 3
  CODE: 16,          // IS_CODE = 1 << 4
  SUBSCRIPT: 32,     // IS_SUBSCRIPT = 1 << 5
  SUPERSCRIPT: 64,   // IS_SUPERSCRIPT = 1 << 6
} as const;

export type TextFormat = typeof TEXT_FORMAT[keyof typeof TEXT_FORMAT];

/**
 * Conversion options
 */
export interface ConversionOptions {
  preserveUnknownNodes?: boolean;
  enableGhostFeatures?: boolean;
  fallbackToHTML?: boolean;
  validateInput?: boolean;
  maxRetries?: number;
}

/**
 * Error codes for different types of conversion failures
 */
export enum ErrorCode {
  NULL_INPUT = 'NULL_INPUT',
  INVALID_INPUT = 'INVALID_INPUT',
  PARSE_FAILED = 'PARSE_FAILED',
  CONVERSION_FAILED = 'CONVERSION_FAILED',
  VALIDATION_FAILED = 'VALIDATION_FAILED',
  UNKNOWN_ERROR = 'UNKNOWN_ERROR'
}

/**
 * Enhanced conversion result with error codes and warnings
 */
export interface EnhancedConversionResult<T> extends ConversionResult<T> {
  errorCode?: ErrorCode;
  warnings?: string[];
  metadata?: {
    processingTime?: number;
    nodeCount?: number;
    retryCount?: number;
  };
}

/**
 * Main Markdown parser class
 */
export class Markdown {
  private editor: LexicalEditor;

  constructor() {
    // Create a headless Lexical editor (no DOM required)
    // Register all the node types we need for markdown parsing
    this.editor = createHeadlessEditor({
      namespace: 'obsidian-ghost-markdown',
      nodes: [
        HeadingNode,
        ListNode,
        ListItemNode,
        QuoteNode,
        CodeNode,
        LinkNode,
        TableNode,
        TableRowNode,
        TableCellNode,
      ],
      onError: (error) => {
        console.error('Lexical editor error:', error);
      },
    });
  }

  /**
   * Convert markdown string to Lexical document
   */
  async markdownToLexical(
    markdown: string,
    options: ConversionOptions = {}
  ): Promise<EnhancedConversionResult<LexicalDocument>> {
    const startTime = Date.now();
    const warnings: string[] = [];

    try {
      // Input validation
      if (markdown === null || markdown === undefined) {
        return {
          success: false,
          error: 'Input cannot be null or undefined',
          errorCode: ErrorCode.NULL_INPUT,
          warnings,
        };
      }

      if (typeof markdown !== 'string') {
        return {
          success: false,
          error: 'Input must be a string',
          errorCode: ErrorCode.INVALID_INPUT,
          warnings,
        };
      }

      // Handle empty or whitespace-only content
      if (!markdown.trim()) {
        return {
          success: true,
          data: {
            root: {
              type: 'root',
              children: [],
              direction: 'ltr',
              format: '',
              indent: 0,
              version: 1
            },
            nodes: []
          },
          warnings,
          metadata: {
            processingTime: Date.now() - startTime,
            nodeCount: 0,
            retryCount: 0,
          }
        };
      }

      // Validate input if requested
      if (options.validateInput) {
        const validationResult = this.validateMarkdownInput(markdown);
        if (!validationResult.isValid) {
          warnings.push(...validationResult.warnings);
          if (validationResult.isCritical) {
            return {
              success: false,
              error: `Input validation failed: ${validationResult.warnings.join(', ')}`,
              errorCode: ErrorCode.VALIDATION_FAILED,
              warnings,
            };
          }
        }
      }

      // Use the same pattern as Lexical tests
      this.editor.update(
        () => {
          $convertFromMarkdownString(markdown, OBSIDIAN_TRANSFORMERS);
        },
        {
          discrete: true,
        }
      );

      // Get the editor state after the update
      const editorState = this.editor.getEditorState();
      const editorStateJSON = editorState.toJSON();

      // Validate the resulting document structure
      if (!editorStateJSON.root || editorStateJSON.root.type !== 'root') {
        return {
          success: false,
          error: 'Invalid document structure: Root node must have type "root"',
          errorCode: ErrorCode.CONVERSION_FAILED,
          warnings,
        };
      }

      // Fix the Lexical structure to match Ghost's expected format
      const fixedRoot = this.fixLexicalStructureForGhost(editorStateJSON.root);

      const document: LexicalDocument = {
        root: fixedRoot,
        nodes: [], // For compatibility with original parser
      };

      // Count nodes for metadata
      const nodeCount = this.countNodes(document.root);

      return {
        success: true,
        data: document,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          nodeCount,
          retryCount: 0,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCode: ErrorCode.UNKNOWN_ERROR,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          retryCount: 0,
        }
      };
    }
  }

  /**
   * Convert Lexical document to markdown string
   */
  async lexicalToMarkdown(
    document: LexicalDocument,
    options: ConversionOptions = {}
  ): Promise<EnhancedConversionResult<string>> {
    const startTime = Date.now();
    const warnings: string[] = [];

    // Note: options parameter is reserved for future use

    try {
      // Input validation
      if (!document) {
        return {
          success: false,
          error: 'Document cannot be null or undefined',
          errorCode: ErrorCode.NULL_INPUT,
          warnings,
        };
      }

      if (!document.root) {
        return {
          success: false,
          error: 'Invalid document structure: Root node must have type "root"',
          errorCode: ErrorCode.INVALID_INPUT,
          warnings,
        };
      }

      // Validate document structure
      const validationResult = this.validateLexicalDocument(document);
      if (!validationResult.isValid) {
        warnings.push(...validationResult.warnings);
        if (validationResult.isCritical) {
          return {
            success: false,
            error: `Document validation failed: ${validationResult.warnings.join(', ')}`,
            errorCode: ErrorCode.VALIDATION_FAILED,
            warnings,
          };
        }
      }

      // Handle empty documents
      if (!document.root.children || document.root.children.length === 0) {
        return {
          success: true,
          data: '',
          warnings,
          metadata: {
            processingTime: Date.now() - startTime,
            nodeCount: 1, // Just the root node
            retryCount: 0,
          }
        };
      }

      // Preprocess Ghost-specific node types before passing to Lexical
      const processedDocument = this.preprocessGhostNodes(document);

      // Restore the editor state from the document
      // parseEditorState expects the full editor state JSON, not just the root
      const fullEditorStateJSON = JSON.stringify({
        root: processedDocument.root
      });
      const editorState = this.editor.parseEditorState(fullEditorStateJSON);
      this.editor.setEditorState(editorState);

      // Convert to markdown using the same pattern as Lexical tests
      const markdown = this.editor.getEditorState().read(() => {
        return $convertToMarkdownString(OBSIDIAN_TRANSFORMERS);
      });

      const nodeCount = this.countNodes(document.root);

      return {
        success: true,
        data: markdown,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          nodeCount,
          retryCount: 0,
        }
      };
    } catch (error) {
      return {
        success: false,
        error: error instanceof Error ? error.message : 'Unknown error',
        errorCode: ErrorCode.UNKNOWN_ERROR,
        warnings,
        metadata: {
          processingTime: Date.now() - startTime,
          retryCount: 0,
        }
      };
    }
  }

  /**
   * Validate markdown input
   * @private
   */
  private validateMarkdownInput(markdown: string): {
    isValid: boolean;
    warnings: string[];
    isCritical: boolean;
  } {
    const warnings: string[] = [];
    let isCritical = false;

    // Check for null bytes and control characters
    if (/[\x00-\x08\x0B\x0C\x0E-\x1F\x7F]/.test(markdown)) {
      warnings.push('Input contains null bytes or control characters');
    }

    // Check for extremely long lines that might cause performance issues
    const lines = markdown.split('\n');
    const maxLineLength = 10000;
    for (let i = 0; i < lines.length; i++) {
      if (lines[i].length > maxLineLength) {
        warnings.push(`Line ${i + 1} is extremely long (${lines[i].length} characters)`);
      }
    }

    // Check for very large documents
    if (markdown.length > 1000000) { // 1MB
      warnings.push('Document is very large and may cause performance issues');
    }

    return {
      isValid: !isCritical,
      warnings,
      isCritical,
    };
  }

  /**
   * Validate lexical document structure
   * @private
   */
  private validateLexicalDocument(document: LexicalDocument): {
    isValid: boolean;
    warnings: string[];
    isCritical: boolean;
  } {
    const warnings: string[] = [];
    let isCritical = false;

    // Check root node
    if (!document.root || document.root.type !== 'root') {
      warnings.push('Root node must have type "root"');
      isCritical = true;
    }

    // Check for valid children structure
    if (document.root.children && Array.isArray(document.root.children)) {
      for (let i = 0; i < document.root.children.length; i++) {
        const child = document.root.children[i];
        if (!child || typeof child.type !== 'string') {
          warnings.push(`Invalid node at position ${i}: missing or invalid type`);
          isCritical = true;
        }
      }
    }

    return {
      isValid: !isCritical,
      warnings,
      isCritical,
    };
  }

  /**
   * Fix Lexical structure to match Ghost's expected format
   * This addresses differences between what Lexical generates and what Ghost expects
   * @private
   */
  private fixLexicalStructureForGhost(node: any): any {
    if (!node || typeof node !== 'object') {
      return node;
    }

    // Create a copy to avoid mutating the original
    const fixed = { ...node };

    // Fix direction: Ghost expects "ltr" instead of null
    if (fixed.direction === null) {
      fixed.direction = "ltr";
    }

    // Convert node types to Ghost's expected format
    if (fixed.type === 'text') {
      fixed.type = 'extended-text';
    } else if (fixed.type === 'heading') {
      fixed.type = 'extended-heading';
    }

    // Remove extra properties that Ghost doesn't expect
    if (fixed.type === 'paragraph') {
      delete fixed.textFormat;
      delete fixed.textStyle;
    }

    // Recursively fix children
    if (fixed.children && Array.isArray(fixed.children)) {
      fixed.children = fixed.children.map((child: any) =>
        this.fixLexicalStructureForGhost(child)
      );
    }

    return fixed;
  }

  /**
   * Count nodes in a document tree
   * @private
   */
  private countNodes(node: any): number {
    let count = 1; // Count the current node
    if (node.children && Array.isArray(node.children)) {
      for (const child of node.children) {
        count += this.countNodes(child);
      }
    }
    return count;
  }

  /**
   * Preprocess Ghost-specific node types to convert them to standard Lexical nodes
   */
  private preprocessGhostNodes(document: LexicalDocument): LexicalDocument {
    const processedDocument = JSON.parse(JSON.stringify(document)); // Deep clone

    if (processedDocument.root && processedDocument.root.children) {
      processedDocument.root.children = this.processNodeArray(processedDocument.root.children);
    }

    return processedDocument;
  }

  /**
   * Process an array of nodes, converting Ghost-specific types
   */
  private processNodeArray(nodes: any[]): any[] {
    return nodes.map(node => this.processNode(node)).filter(node => node !== null);
  }

  /**
   * Process a single node, converting Ghost-specific types to standard Lexical nodes
   */
  private processNode(node: any): any {
    if (!node || typeof node !== 'object') {
      return node;
    }

    // Handle Ghost callout nodes
    if (node.type === 'callout') {
      return this.convertGhostCalloutToQuote(node);
    }

    // Handle Ghost markdown nodes
    if (node.type === 'markdown') {
      return this.convertGhostMarkdownToParagraph(node);
    }

    // Handle Ghost extended-heading nodes
    if (node.type === 'extended-heading') {
      return this.convertExtendedHeading(node);
    }

    // Handle Ghost extended-text nodes
    if (node.type === 'extended-text') {
      return this.convertExtendedText(node);
    }

    // Process children recursively
    if (node.children && Array.isArray(node.children)) {
      node.children = this.processNodeArray(node.children);
    }

    return node;
  }

  /**
   * Convert Ghost callout node to standard quote node with callout formatting
   */
  private convertGhostCalloutToQuote(calloutNode: any): any {
    // Extract text content from HTML
    const textContent = this.extractTextFromHTML(calloutNode.calloutText || '');

    // Create a quote node with callout-style content
    return {
      type: 'quote',
      children: [
        {
          type: 'paragraph',
          children: [
            {
              type: 'text',
              text: `${calloutNode.calloutEmoji || '💡'} ${textContent}`,
              detail: 0,
              format: 0,
              mode: 'normal',
              style: '',
              version: 1
            }
          ],
          direction: 'ltr',
          format: '',
          indent: 0,
          version: 1
        }
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Convert Ghost markdown node to paragraph with the raw markdown content
   */
  private convertGhostMarkdownToParagraph(markdownNode: any): any {
    // For now, convert markdown tables to plain text representation
    // In the future, we could parse the markdown and convert to proper table nodes
    const markdownContent = markdownNode.markdown || '';

    return {
      type: 'paragraph',
      children: [
        {
          type: 'text',
          text: markdownContent,
          detail: 0,
          format: 16, // Code format to preserve table structure
          mode: 'normal',
          style: '',
          version: 1
        }
      ],
      direction: 'ltr',
      format: '',
      indent: 0,
      version: 1
    };
  }

  /**
   * Convert Ghost extended-heading to standard heading
   */
  private convertExtendedHeading(extendedHeading: any): any {
    return {
      type: 'heading',
      tag: extendedHeading.tag || 'h1',
      children: extendedHeading.children ? this.processNodeArray(extendedHeading.children) : [],
      direction: extendedHeading.direction || 'ltr',
      format: extendedHeading.format || '',
      indent: extendedHeading.indent || 0,
      version: extendedHeading.version || 1
    };
  }

  /**
   * Convert Ghost extended-text to standard text
   */
  private convertExtendedText(extendedText: any): any {
    return {
      type: 'text',
      text: extendedText.text || '',
      detail: extendedText.detail || 0,
      format: extendedText.format || 0,
      mode: extendedText.mode || 'normal',
      style: extendedText.style || '',
      version: extendedText.version || 1
    };
  }

  /**
   * Extract plain text from HTML content
   */
  private extractTextFromHTML(html: string): string {
    // Simple HTML tag removal - in a real implementation, you might want to use a proper HTML parser
    return html
      .replace(/<[^>]*>/g, '') // Remove HTML tags
      .replace(/&nbsp;/g, ' ') // Replace non-breaking spaces
      .replace(/&amp;/g, '&') // Replace HTML entities
      .replace(/&lt;/g, '<')
      .replace(/&gt;/g, '>')
      .replace(/&quot;/g, '"')
      .trim();
  }

  /**
   * Clean up resources
   */
  destroy(): void {
    // Clean up the editor if needed
  }
}

/**
 * Convenience functions for one-off conversions
 * These create fresh editor instances for each conversion to avoid state issues
 */

/**
 * Convert markdown to Lexical document
 */
export async function markdownToLexical(
  markdown: string,
  options?: ConversionOptions
): Promise<EnhancedConversionResult<LexicalDocument>> {
  const parser = new Markdown();
  try {
    return await parser.markdownToLexical(markdown, options);
  } finally {
    parser.destroy();
  }
}

/**
 * Convert Lexical document to markdown
 */
export async function lexicalToMarkdown(
  document: LexicalDocument,
  options?: ConversionOptions
): Promise<EnhancedConversionResult<string>> {
  const parser = new Markdown();
  try {
    return await parser.lexicalToMarkdown(document, options);
  } finally {
    parser.destroy();
  }
}

/**
 * Round-trip test: markdown -> lexical -> markdown
 */
export async function roundTrip(markdown: string): Promise<ConversionResult<string>> {
  const parser = new Markdown();
  try {
    const lexicalResult = await parser.markdownToLexical(markdown);
    if (!lexicalResult.success || !lexicalResult.data) {
      return {
        success: false,
        error: `Failed to convert to Lexical: ${lexicalResult.error}`,
      };
    }

    const markdownResult = await parser.lexicalToMarkdown(lexicalResult.data);
    if (!markdownResult.success || markdownResult.data === undefined) {
      return {
        success: false,
        error: `Failed to convert back to markdown: ${markdownResult.error}`,
      };
    }

    return {
      success: true,
      data: markdownResult.data,
    };
  } finally {
    parser.destroy();
  }
}
